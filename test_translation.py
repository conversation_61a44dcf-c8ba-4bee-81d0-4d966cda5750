#!/usr/bin/env python3
"""
Test script to verify Tamil to English translation functionality
"""

import re
from googletrans import Translator

# Tamil unicode range
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def test_translation():
    """Test the translation functionality with sample Tamil text"""
    translator = Translator()
    
    # Sample Tamil text (Hello, how are you?)
    test_texts = [
        "வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?",
        "நான் நன்றாக இருக்கிறேன்",
        "திரைப்படம் மிகவும் நன்றாக இருந்தது"
    ]
    
    print("🧪 Testing Tamil to English translation:")
    print("=" * 50)
    
    for i, tamil_text in enumerate(test_texts, 1):
        print(f"\nTest {i}:")
        print(f"Tamil: {tamil_text}")
        
        try:
            result = translator.translate(tamil_text, src='ta', dest='en')
            english_text = result.text
            print(f"English: {english_text}")
            
            # Check for Tamil characters in output
            has_tamil = bool(tamil_chars_re.search(english_text))
            if has_tamil:
                print("❌ FAIL: Tamil characters found in translation!")
            else:
                print("✅ PASS: No Tamil characters in translation")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    test_translation()
