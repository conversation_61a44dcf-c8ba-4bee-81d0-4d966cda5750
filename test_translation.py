#!/usr/bin/env python3
"""
Test script to verify Tamil to English transliteration functionality
"""

import re
from indic_transliteration.sanscript import transliterate
from indic_transliteration import sanscript

# Tamil unicode range
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def test_transliteration():
    """Test the transliteration functionality with sample Tamil text"""

    # Sample Tamil text
    test_texts = [
        "வணக்கம், நீங்கள் எப்படி இருக்கிறீர்கள்?",
        "நான் நன்றாக இருக்கிறேன்",
        "திரைப்படம் மிகவும் நன்றாக இருந்தது",
        "அவன் வீட்டிற்கு சென்றான்",
        "இது ஒரு சோதனை"
    ]

    print("🧪 Testing Tamil to Roman transliteration:")
    print("=" * 60)

    for i, tamil_text in enumerate(test_texts, 1):
        print(f"\nTest {i}:")
        print(f"Tamil: {tamil_text}")

        try:
            # Transliterate using ITRANS scheme
            roman_text = transliterate(tamil_text, sanscript.TAMIL, sanscript.ITRANS)
            print(f"Roman: {roman_text}")

            # Check for Tamil characters in output
            has_tamil = bool(tamil_chars_re.search(roman_text))
            if has_tamil:
                print("❌ FAIL: Tamil characters found in transliteration!")
                tamil_found = tamil_chars_re.findall(roman_text)
                print(f"Tamil chars found: {set(tamil_found)}")
            else:
                print("✅ PASS: No Tamil characters in transliteration")

        except Exception as e:
            print(f"❌ ERROR: {e}")

    print("\n" + "=" * 60)
    print("Test completed!")

if __name__ == "__main__":
    test_transliteration()
