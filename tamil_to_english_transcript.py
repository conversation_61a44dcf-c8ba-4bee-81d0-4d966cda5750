import re
from indic_transliteration.sanscript import transliterate
from indic_transliteration import sanscript
import os

# Tamil unicode range
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def transliterate_char_by_char(line):
    result = []
    buffer = ''

    for ch in line:
        if tamil_chars_re.match(ch):
            buffer += ch  # accumulate Tamil chars
        else:
            # flush buffer if any Tamil chars collected
            if buffer:
                # transliterate accumulated Tamil chars at once
                result.append(transliterate(buffer, sanscript.TAMIL, sanscript.ITRANS))
                buffer = ''
            result.append(ch)  # append non-Tamil char as is

    # flush buffer at end of line
    if buffer:
        result.append(transliterate(buffer, sanscript.TAMIL, sanscript.ITRANS))

    return ''.join(result)

def generate_output_path(input_path):
    base, ext = os.path.splitext(input_path)
    return f"{base}-en{ext}"

def main():
    input_path = input("Enter path to Tamil .srt file: ").strip('"')
    output_path = input("Enter output path (blank for default '-en.srt'): ").strip('"')
    if not output_path:
        output_path = generate_output_path(input_path)

    with open(input_path, 'r', encoding='utf-8') as infile:
        lines = infile.readlines()

    with open(output_path, 'w', encoding='utf-8') as outfile:
        for line in lines:
            # Write numbers, timestamps, blank lines as is
            if re.match(r'^\d+$', line.strip()) or '-->' in line or line.strip() == '':
                outfile.write(line)
            else:
                out_line = transliterate_char_by_char(line)
                outfile.write(out_line)

    print(f"\n✅ Done! Output saved: {output_path}")

if __name__ == "__main__":
    main()
