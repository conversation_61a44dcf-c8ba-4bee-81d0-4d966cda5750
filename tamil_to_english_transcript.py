import re
import os
from googletrans import Translator
import time

# Tamil unicode range - includes all Tamil characters
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def has_tamil_characters(text):
    """Check if text contains any Tamil characters"""
    return bool(tamil_chars_re.search(text))

def translate_tamil_to_english(text, translator):
    """Translate Tamil text to English using Google Translate"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        # Translate Tamil to English
        result = translator.translate(text, src='ta', dest='en')
        translated_text = result.text

        # Verify no Tamil characters remain
        if has_tamil_characters(translated_text):
            print(f"Warning: Tamil characters found in translation: {translated_text}")
            # Fallback: remove Tamil characters if they somehow remain
            translated_text = tamil_chars_re.sub('', translated_text)

        return translated_text

    except Exception as e:
        print(f"Translation error for text '{text}': {e}")
        # Fallback: remove Tamil characters
        return tamil_chars_re.sub('', text)

def generate_output_path(input_path):
    """Generate output path with -en suffix"""
    base, ext = os.path.splitext(input_path)
    return f"{base}-en{ext}"

def verify_no_tamil_in_file(file_path):
    """Verify that the output file contains no Tamil characters"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    tamil_found = tamil_chars_re.findall(content)
    if tamil_found:
        print(f"❌ Warning: Found {len(tamil_found)} Tamil characters in output file!")
        print(f"Tamil characters found: {set(tamil_found)}")
        return False
    else:
        print("✅ Verification passed: No Tamil characters found in output file!")
        return True

def main():
    # Use the provided file path or ask for input
    default_input = r"C:\Users\<USER>\Videos\16.Extremes.(Dhuruvangal.Pathinaaru).2016.1080p.WEB-DL.x264.AC3.HORiZON-ArtSubs_tamil.srt"

    input_path = input(f"Enter path to Tamil .srt file (or press Enter for default): ").strip('"')
    if not input_path:
        input_path = default_input

    output_path = input("Enter output path (blank for default '-en.srt'): ").strip('"')
    if not output_path:
        output_path = generate_output_path(input_path)

    # Check if input file exists
    if not os.path.exists(input_path):
        print(f"❌ Error: Input file not found: {input_path}")
        return

    print(f"📖 Reading Tamil subtitle file: {input_path}")
    print(f"🎯 Output will be saved to: {output_path}")

    # Initialize translator
    translator = Translator()

    try:
        with open(input_path, 'r', encoding='utf-8') as infile:
            lines = infile.readlines()

        print(f"📝 Processing {len(lines)} lines...")

        with open(output_path, 'w', encoding='utf-8') as outfile:
            for i, line in enumerate(lines, 1):
                # Write numbers, timestamps, blank lines as is
                if re.match(r'^\d+$', line.strip()) or '-->' in line or line.strip() == '':
                    outfile.write(line)
                else:
                    # Translate Tamil text to English
                    translated_line = translate_tamil_to_english(line, translator)
                    outfile.write(translated_line)

                    # Add small delay to avoid rate limiting
                    if i % 10 == 0:
                        time.sleep(0.1)
                        print(f"Processed {i}/{len(lines)} lines...")

        print(f"\n✅ Translation completed! Output saved: {output_path}")

        # Verify the output contains no Tamil characters
        print("\n🔍 Verifying output...")
        verify_no_tamil_in_file(output_path)

    except Exception as e:
        print(f"❌ Error processing file: {e}")

if __name__ == "__main__":
    main()
