import re
import os
from indic_transliteration.sanscript import transliterate
from indic_transliteration import sanscript
import time
from googletrans import Translator
from tqdm import tqdm

# Tamil unicode range - includes all Tamil characters
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def has_tamil_characters(text):
    """Check if text contains any Tamil characters"""
    return bool(tamil_chars_re.search(text))

def transliterate_tamil_char_by_char(text):
    """Transliterate Tamil text character by character with multiple scheme fallbacks"""
    result = []
    i = 0

    while i < len(text):
        char = text[i]

        # If it's not a Tamil character, keep as is
        if not tamil_chars_re.match(char):
            result.append(char)
            i += 1
            continue

        # Try to transliterate Tamil character
        transliterated = None

        # Try different schemes in order of preference
        schemes_to_try = [
            sanscript.ISO,      # ISO 15919 - most comprehensive and accurate
            sanscript.IAST,     # International Alphabet of Sanskrit Transliteration
            sanscript.HK        # Harvard-Kyoto - good fallback
            # Note: ITRANS removed as it drops characters
        ]

        for scheme in schemes_to_try:
            try:
                temp_result = transliterate(char, sanscript.TAMIL, scheme)
                # Check if transliteration was successful (not empty and different from input)
                if temp_result and temp_result != char and not has_tamil_characters(temp_result):
                    transliterated = temp_result
                    break
            except:
                continue

        # If no scheme worked, try with surrounding context (up to 3 chars)
        if not transliterated:
            for context_size in [2, 3]:
                if i + context_size <= len(text):
                    context = text[i:i+context_size]
                    for scheme in schemes_to_try:
                        try:
                            temp_result = transliterate(context, sanscript.TAMIL, scheme)
                            if temp_result and not has_tamil_characters(temp_result):
                                # Take only the first character's worth of transliteration
                                transliterated = temp_result[0] if temp_result else None
                                break
                        except:
                            continue
                    if transliterated:
                        break

        # If still no transliteration, use Unicode name as fallback
        if not transliterated:
            try:
                import unicodedata
                unicode_name = unicodedata.name(char, '')
                if 'TAMIL' in unicode_name:
                    # Extract the letter name and create a simple transliteration
                    name_parts = unicode_name.split()
                    if len(name_parts) >= 3:  # e.g., "TAMIL LETTER NA"
                        letter_name = name_parts[2].lower()
                        transliterated = letter_name
                    else:
                        transliterated = f"[{char}]"  # Keep in brackets as last resort
                else:
                    transliterated = f"[{char}]"
            except:
                transliterated = f"[{char}]"  # Absolute fallback

        result.append(transliterated)
        i += 1

    return ''.join(result)

def translate_tamil_words_to_english(text, translator):
    """Translate individual Tamil words to English"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        # Translate the whole text to English
        result = translator.translate(text, src='ta', dest='en')
        return result.text

    except Exception as e:
        print(f"Translation error for text '{text}': {e}")
        return text

def create_google_transliteration_and_translation(text, translator, pbar=None):
    """Use local transliteration + Google translation - FAST METHOD"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        clean_text = text.strip()

        try:
            # Method 1: Use local transliteration (fast and accurate)
            transliterated_text = transliterate_tamil_to_roman(clean_text)

            # Method 2: Get English translation from Google
            translation_result = translator.translate(clean_text, src='ta', dest='en')
            translated_text = translation_result.text

            # Format as requested: transliteration on first line, translation on second line
            result = f"{transliterated_text}\n{translated_text}\n"
            return result

        except Exception as e:
            if pbar:
                tqdm.write(f"Fast method failed for: '{clean_text[:50]}...': {e}")

            # Fallback: Just transliteration if translation fails
            try:
                transliterated_text = transliterate_tamil_to_roman(clean_text)
                result = f"{transliterated_text}\n[Translation failed]\n"
                return result
            except Exception as e2:
                if pbar:
                    tqdm.write(f"Fallback also failed: {e2}")
                return transliterate_tamil_to_roman(text)

    except Exception as e:
        if pbar:
            tqdm.write(f"Error in fast method for '{text[:50]}...': {e}")
        else:
            print(f"Error in fast method for '{text[:50]}...': {e}")
        return transliterate_tamil_to_roman(text)

def create_transliteration_with_translation(text, translator, pbar=None):
    """Create both transliteration and word-by-word translation"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        # First get the transliteration
        transliterated_text = transliterate_tamil_to_roman(text)

        # Get word-by-word translation
        # Split text into words (preserving punctuation)
        words = re.findall(r'\S+|\s+', text)
        tamil_words = []
        transliterated_words = []
        translated_words = []

        # Create progress bar for word processing if many words
        word_list = [word for word in words if not word.isspace()]
        if len(word_list) > 5 and pbar:
            word_pbar = tqdm(word_list, desc="Processing words", leave=False, disable=len(word_list) < 10)
        else:
            word_pbar = word_list

        for word in word_pbar:
            # Remove punctuation for translation but keep it for display
            clean_word = re.sub(r'[^\u0B80-\u0BFF\u0B00-\u0B7F]+', '', word)

            if clean_word and has_tamil_characters(clean_word):
                # Transliterate the word
                try:
                    trans_word = transliterate(clean_word, sanscript.TAMIL, sanscript.ISO)
                    transliterated_words.append(trans_word)
                except:
                    transliterated_words.append(clean_word)

                # Translate the word
                try:
                    translated = translator.translate(clean_word, src='ta', dest='en').text
                    translated_words.append(f"'{translated}'")
                except:
                    translated_words.append(f"'{clean_word}'")

                tamil_words.append(clean_word)

        # Close word progress bar if it was created
        if hasattr(word_pbar, 'close'):
            word_pbar.close()

        # Create the formatted output
        if transliterated_words and translated_words:
            transliteration_line = ' '.join(transliterated_words)
            translation_line = ' '.join(translated_words)

            # Format as requested: transliteration on first line, translations on second line
            result = f"{transliteration_line}\n{translation_line}\n"
            return result
        else:
            return transliterated_text

    except Exception as e:
        if pbar:
            tqdm.write(f"Error creating transliteration with translation for '{text}': {e}")
        else:
            print(f"Error creating transliteration with translation for '{text}': {e}")
        return transliterate_tamil_to_roman(text)

def transliterate_tamil_to_roman(text):
    """Transliterate Tamil text to Roman script with comprehensive character preservation"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        # First try ISO scheme for the whole text (most comprehensive and accurate)
        try:
            transliterated_text = transliterate(text, sanscript.TAMIL, sanscript.ISO)
            # If successful and no Tamil characters remain, return it
            if not has_tamil_characters(transliterated_text):
                return transliterated_text
        except Exception as e:
            print(f"ISO transliteration failed for '{text.strip()}': {e}")
            pass

        # If ISO scheme failed or left Tamil characters, try character-by-character
        transliterated_text = transliterate_tamil_char_by_char(text)

        # Final check: if any Tamil characters still remain, show warning but don't remove them
        if has_tamil_characters(transliterated_text):
            remaining_tamil = tamil_chars_re.findall(transliterated_text)
            print(f"Warning: Some Tamil characters couldn't be transliterated: {set(remaining_tamil)}")
            print(f"Original: {text.strip()}")
            print(f"Result: {transliterated_text.strip()}")

        return transliterated_text

    except Exception as e:
        print(f"Transliteration error for text '{text}': {e}")
        # Fallback: try character by character
        return transliterate_tamil_char_by_char(text)

def generate_output_path(input_path):
    """Generate output path with -en suffix and handle existing files"""
    base, ext = os.path.splitext(input_path)
    output_path = f"{base}-en{ext}"

    # If file exists, add incremental numbers
    counter = 1
    original_output = output_path

    while os.path.exists(output_path):
        base_name, extension = os.path.splitext(original_output)
        output_path = f"{base_name}_{counter}{extension}"
        counter += 1

    return output_path

def verify_no_tamil_in_file(file_path):
    """Verify that the output file contains no Tamil characters"""
    print("🔍 Verifying output file...")

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Use tqdm to show verification progress
    lines = content.split('\n')
    tamil_found = []

    with tqdm(lines, desc="Verifying lines", unit="line") as pbar:
        for line in pbar:
            found_in_line = tamil_chars_re.findall(line)
            tamil_found.extend(found_in_line)

    if tamil_found:
        print(f"❌ Warning: Found {len(tamil_found)} Tamil characters in output file!")
        print(f"Tamil characters found: {set(tamil_found)}")
        return False
    else:
        print("✅ Verification passed: No Tamil characters found in output file!")
        return True

def main():
    # Use the provided file path or ask for input
    default_input = r"C:\Users\<USER>\Videos\16.Extremes.(Dhuruvangal.Pathinaaru).2016.1080p.WEB-DL.x264.AC3.HORiZON-ArtSubs_tamil.srt"

    input_path = input(f"Enter path to Tamil .srt file (or press Enter for default): ").strip('"')
    if not input_path:
        input_path = default_input

    output_path = input("Enter output path (blank for default '-en.srt'): ").strip('"')
    if not output_path:
        output_path = generate_output_path(input_path)

    # Check if input file exists
    if not os.path.exists(input_path):
        print(f"❌ Error: Input file not found: {input_path}")
        return

    print(f"📖 Reading Tamil subtitle file: {input_path}")
    print(f"🎯 Output will be saved to: {output_path}")

    # Ask user for output format preference
    print("\nChoose output format:")
    print("1. Transliteration only (Tamil script → Roman letters) - ⚡ FASTEST")
    print("2. Google Transliteration + Translation (2 API calls per line) - 🚀 FAST")
    print("3. Word-by-word Translation (many API calls) - 🐌 SLOW but detailed")

    while True:
        choice = input("Enter choice (1, 2, or 3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("Please enter 1, 2, or 3")

    include_translation = (choice in ['2', '3'])
    use_google_transliteration = (choice == '2')
    word_by_word = (choice == '3')

    # Initialize translator if needed
    translator = None
    if include_translation:
        print("🔄 Initializing translator...")
        translator = Translator()

    try:
        # Read file with progress
        print("📖 Reading input file...")
        with open(input_path, 'r', encoding='utf-8') as infile:
            lines = infile.readlines()

        print(f"📝 Processing {len(lines)} lines...")

        # Create main progress bar
        with tqdm(lines, desc="Processing lines", unit="line") as pbar:
            with open(output_path, 'w', encoding='utf-8') as outfile:
                for i, line in enumerate(pbar, 1):
                    # Update progress bar description
                    pbar.set_description(f"Processing line {i}")

                    # Write numbers, timestamps, blank lines as is
                    if re.match(r'^\d+$', line.strip()) or '-->' in line or line.strip() == '':
                        outfile.write(line)
                        pbar.set_postfix({"Status": "Metadata"})
                    else:
                        if include_translation:
                            if use_google_transliteration:
                                # Use Google for both transliteration and translation (FASTEST)
                                pbar.set_postfix({"Status": "Google Trans+Translit"})
                                processed_line = create_google_transliteration_and_translation(line, translator, pbar)
                                # Minimal delay for Google method
                                if i % 20 == 0:
                                    time.sleep(0.1)
                            else:
                                # Word-by-word translation (SLOWER)
                                pbar.set_postfix({"Status": "Word-by-word"})
                                processed_line = create_transliteration_with_translation(line, translator, pbar)
                                # More delay for word-by-word to avoid rate limiting
                                time.sleep(0.2)

                            outfile.write(processed_line)
                        else:
                            # Transliterate Tamil text to Roman script only
                            pbar.set_postfix({"Status": "Transliterating"})
                            transliterated_line = transliterate_tamil_to_roman(line)
                            outfile.write(transliterated_line)

        if include_translation:
            if use_google_transliteration:
                print(f"\n✅ Google Transliteration + Translation completed! Output saved: {output_path}")
            else:
                print(f"\n✅ Word-by-word Translation completed! Output saved: {output_path}")
        else:
            print(f"\n✅ Transliteration completed! Output saved: {output_path}")

        # Verify the output contains no Tamil characters
        print()
        verify_no_tamil_in_file(output_path)

    except Exception as e:
        print(f"❌ Error processing file: {e}")

if __name__ == "__main__":
    main()
