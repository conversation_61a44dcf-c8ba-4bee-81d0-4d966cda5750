import re
import os
import time
from googletrans import Translator
from tqdm import tqdm

# Tamil unicode range - includes all Tamil characters
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def has_tamil_characters(text):
    """Check if text contains any Tamil characters"""
    return bool(tamil_chars_re.search(text))

# Comprehensive Tamil to Roman character mapping
TAMIL_TO_ROMAN = {
    # Vowels (independent)
    'அ': 'a', 'ஆ': 'aa', 'இ': 'i', 'ஈ': 'ii', 'உ': 'u', 'ஊ': 'uu',
    'எ': 'e', 'ஏ': 'ee', 'ஐ': 'ai', 'ஒ': 'o', 'ஓ': 'oo', 'ஔ': 'au',

    # Consonants (with inherent 'a')
    'க': 'ka', 'ங': 'nga', 'ச': 'cha', 'ஞ': 'nya', 'ட': 'ta', 'ண': 'na',
    'த': 'tha', 'ந': 'nha', 'ன': 'na', 'ப': 'pa', 'ம': 'ma', 'ய': 'ya',
    'ர': 'ra', 'ல': 'la', 'வ': 'va', 'ழ': 'zha', 'ள': 'lla', 'ற': 'rra',
    'ஜ': 'ja', 'ஷ': 'sha', 'ஸ': 'sa', 'ஹ': 'ha', 'க்ஷ': 'ksha',

    # Vowel signs (dependent)
    'ா': 'aa', 'ி': 'i', 'ீ': 'ii', 'ு': 'u', 'ூ': 'uu', 'ெ': 'e',
    'ே': 'ee', 'ை': 'ai', 'ொ': 'o', 'ோ': 'oo', 'ௌ': 'au',

    # Special characters
    '்': '', 'ஃ': 'h',  # Virama (removes inherent vowel)

    # Tamil numerals
    '௧': '1', '௨': '2', '௩': '3', '௪': '4', '௫': '5',
    '௬': '6', '௭': '7', '௮': '8', '௯': '9', '௦': '0',

    # Additional consonant combinations
    'ஶ': 'sha', 'ஜ்ஞ': 'gya', 'க்ஷ': 'ksha',

    # Common conjuncts (consonant clusters)
    'ந்த': 'ntha', 'ன்ற': 'nra', 'ல்ல': 'lla', 'ற்ற': 'rra',
    'க்க': 'kka', 'ப்ப': 'ppa', 'த்த': 'ttha', 'ம்ம': 'mma'
}

def simple_tamil_transliterate(text):
    """Simple but accurate Tamil to Roman transliteration using character mapping"""
    result = []
    i = 0

    while i < len(text):
        char = text[i]
        found = False

        # Check for longer combinations first (3 chars, then 2 chars)
        for length in [3, 2]:
            if i + length <= len(text):
                multi_char = text[i:i+length]
                if multi_char in TAMIL_TO_ROMAN:
                    result.append(TAMIL_TO_ROMAN[multi_char])
                    i += length
                    found = True
                    break

        if found:
            continue

        # Single character mapping
        if char in TAMIL_TO_ROMAN:
            result.append(TAMIL_TO_ROMAN[char])
        elif tamil_chars_re.match(char):
            # Unknown Tamil character - show Unicode info for debugging
            import unicodedata
            try:
                char_name = unicodedata.name(char, f'U+{ord(char):04X}')
                print(f"Unknown Tamil character: '{char}' ({char_name})")
            except:
                print(f"Unknown Tamil character: '{char}' (U+{ord(char):04X})")
            result.append(f'[{char}]')
        else:
            # Non-Tamil character - keep as is
            result.append(char)

        i += 1

    return ''.join(result)

def google_transliterate_tamil(text, translator):
    """Get transliteration using simple mapping + Google for unknown characters"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        clean_text = text.strip()

        # Use simple character mapping first (fast and accurate)
        transliterated = simple_tamil_transliterate(clean_text)

        # If there are still bracketed unknown characters, try Google for those
        if '[' in transliterated and ']' in transliterated:
            try:
                # Get Google translation as fallback for unknown parts
                google_result = translator.translate(clean_text, src='ta', dest='en')

                # If Google result looks phonetic (short, no common words), use it
                google_text = google_result.text
                common_words = ['the', 'and', 'is', 'are', 'was', 'were', 'have', 'has', 'had', 'will', 'would', 'could', 'should']

                if len(google_text.split()) <= 3 and not any(word in google_text.lower() for word in common_words):
                    return google_text
            except:
                pass

        return transliterated

    except Exception as e:
        print(f"Transliteration error for '{text}': {e}")
        return text

def translate_tamil_words_to_english(text, translator):
    """Translate individual Tamil words to English"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        # Translate the whole text to English
        result = translator.translate(text, src='ta', dest='en')
        return result.text

    except Exception as e:
        print(f"Translation error for text '{text}': {e}")
        return text

def create_google_transliteration_and_translation(text, translator, pbar=None):
    """Use Google for both transliteration and translation - FAST METHOD"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        clean_text = text.strip()

        try:
            # Method 1: Get Google transliteration
            transliterated_text = google_transliterate_tamil(clean_text, translator)

            # Method 2: Get English translation from Google
            translation_result = translator.translate(clean_text, src='ta', dest='en')
            translated_text = translation_result.text

            # Format as requested: transliteration on first line, translation on second line
            result = f"{transliterated_text}\n{translated_text}\n"
            return result

        except Exception as e:
            if pbar:
                tqdm.write(f"Google method failed for: '{clean_text[:50]}...': {e}")

            # Fallback: Just translation if transliteration fails
            try:
                translation_result = translator.translate(clean_text, src='ta', dest='en')
                translated_text = translation_result.text
                result = f"{translated_text}\n{translated_text}\n"  # Use translation for both lines
                return result
            except Exception as e2:
                if pbar:
                    tqdm.write(f"Fallback also failed: {e2}")
                return clean_text

    except Exception as e:
        if pbar:
            tqdm.write(f"Error in Google method for '{text[:50]}...': {e}")
        else:
            print(f"Error in Google method for '{text[:50]}...': {e}")
        return text

def create_transliteration_with_translation(text, translator, pbar=None):
    """Create both transliteration and word-by-word translation"""
    try:
        # Skip if text is empty or only whitespace
        if not text.strip():
            return text

        # Skip if text doesn't contain Tamil characters
        if not has_tamil_characters(text):
            return text

        # First get the transliteration using Google
        transliterated_text = google_transliterate_tamil(text, translator)

        # Get word-by-word translation
        # Split text into words (preserving punctuation)
        words = re.findall(r'\S+|\s+', text)
        tamil_words = []
        transliterated_words = []
        translated_words = []

        # Create progress bar for word processing if many words
        word_list = [word for word in words if not word.isspace()]
        if len(word_list) > 5 and pbar:
            word_pbar = tqdm(word_list, desc="Processing words", leave=False, disable=len(word_list) < 10)
        else:
            word_pbar = word_list

        for word in word_pbar:
            # Remove punctuation for translation but keep it for display
            clean_word = re.sub(r'[^\u0B80-\u0BFF\u0B00-\u0B7F]+', '', word)

            if clean_word and has_tamil_characters(clean_word):
                # Transliterate the word using Google
                try:
                    trans_word = google_transliterate_tamil(clean_word, translator)
                    transliterated_words.append(trans_word)
                except:
                    transliterated_words.append(clean_word)

                # Translate the word
                try:
                    translated = translator.translate(clean_word, src='ta', dest='en').text
                    translated_words.append(f"'{translated}'")
                except:
                    translated_words.append(f"'{clean_word}'")

                tamil_words.append(clean_word)

        # Close word progress bar if it was created
        if hasattr(word_pbar, 'close'):
            word_pbar.close()

        # Create the formatted output
        if transliterated_words and translated_words:
            transliteration_line = ' '.join(transliterated_words)
            translation_line = ' '.join(translated_words)

            # Format as requested: transliteration on first line, translations on second line
            result = f"{transliteration_line}\n{translation_line}\n"
            return result
        else:
            return transliterated_text

    except Exception as e:
        if pbar:
            tqdm.write(f"Error creating transliteration with translation for '{text}': {e}")
        else:
            print(f"Error creating transliteration with translation for '{text}': {e}")
        return transliterate_tamil_to_roman(text)

def transliterate_tamil_to_roman(text, translator):
    """Transliterate Tamil text to Roman script using Google Translate"""
    return google_transliterate_tamil(text, translator)

def generate_output_path(input_path):
    """Generate output path with -en suffix and handle existing files"""
    base, ext = os.path.splitext(input_path)
    output_path = f"{base}-en{ext}"

    # If file exists, add incremental numbers
    counter = 1
    original_output = output_path

    while os.path.exists(output_path):
        base_name, extension = os.path.splitext(original_output)
        output_path = f"{base_name}_{counter}{extension}"
        counter += 1

    return output_path

def verify_no_tamil_in_file(file_path):
    """Verify that the output file contains no Tamil characters"""
    print("🔍 Verifying output file...")

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Use tqdm to show verification progress
    lines = content.split('\n')
    tamil_found = []

    with tqdm(lines, desc="Verifying lines", unit="line") as pbar:
        for line in pbar:
            found_in_line = tamil_chars_re.findall(line)
            tamil_found.extend(found_in_line)

    if tamil_found:
        print(f"❌ Warning: Found {len(tamil_found)} Tamil characters in output file!")
        print(f"Tamil characters found: {set(tamil_found)}")
        return False
    else:
        print("✅ Verification passed: No Tamil characters found in output file!")
        return True

def main():
    # Use the provided file path or ask for input
    default_input = r"C:\Users\<USER>\Videos\16.Extremes.(Dhuruvangal.Pathinaaru).2016.1080p.WEB-DL.x264.AC3.HORiZON-ArtSubs_tamil.srt"

    input_path = input(f"Enter path to Tamil .srt file (or press Enter for default): ").strip('"')
    if not input_path:
        input_path = default_input

    output_path = input("Enter output path (blank for default '-en.srt'): ").strip('"')
    if not output_path:
        output_path = generate_output_path(input_path)

    # Check if input file exists
    if not os.path.exists(input_path):
        print(f"❌ Error: Input file not found: {input_path}")
        return

    print(f"📖 Reading Tamil subtitle file: {input_path}")
    print(f"🎯 Output will be saved to: {output_path}")

    # Ask user for output format preference
    print("\nChoose output format:")
    print("1. Google Transliteration only - 🚀 FAST (1 API call per line)")
    print("2. Google Transliteration + Translation - 🚀 FAST (2 API calls per line)")
    print("3. Word-by-word Translation - 🐌 SLOW but detailed (many API calls)")

    while True:
        choice = input("Enter choice (1, 2, or 3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("Please enter 1, 2, or 3")

    include_translation = (choice in ['2', '3'])
    use_google_transliteration = (choice == '2')
    word_by_word = (choice == '3')

    # Initialize translator (needed for all options now)
    print("🔄 Initializing Google Translator...")
    translator = Translator()

    try:
        # Read file with progress
        print("📖 Reading input file...")
        with open(input_path, 'r', encoding='utf-8') as infile:
            lines = infile.readlines()

        print(f"📝 Processing {len(lines)} lines...")

        # Create main progress bar
        with tqdm(lines, desc="Processing lines", unit="line") as pbar:
            with open(output_path, 'w', encoding='utf-8') as outfile:
                for i, line in enumerate(pbar, 1):
                    # Update progress bar description
                    pbar.set_description(f"Processing line {i}")

                    # Write numbers, timestamps, blank lines as is
                    if re.match(r'^\d+$', line.strip()) or '-->' in line or line.strip() == '':
                        outfile.write(line)
                        pbar.set_postfix({"Status": "Metadata"})
                    else:
                        if include_translation:
                            if use_google_transliteration:
                                # Use Google for both transliteration and translation (FASTEST)
                                pbar.set_postfix({"Status": "Google Trans+Translit"})
                                processed_line = create_google_transliteration_and_translation(line, translator, pbar)
                                # Minimal delay for Google method
                                if i % 20 == 0:
                                    time.sleep(0.1)
                            else:
                                # Word-by-word translation (SLOWER)
                                pbar.set_postfix({"Status": "Word-by-word"})
                                processed_line = create_transliteration_with_translation(line, translator, pbar)
                                # More delay for word-by-word to avoid rate limiting
                                time.sleep(0.2)

                            outfile.write(processed_line)
                        else:
                            # Transliterate Tamil text to Roman script only using Google
                            pbar.set_postfix({"Status": "Google Transliterating"})
                            transliterated_line = transliterate_tamil_to_roman(line, translator)
                            outfile.write(transliterated_line)

        if include_translation:
            if use_google_transliteration:
                print(f"\n✅ Google Transliteration + Translation completed! Output saved: {output_path}")
            else:
                print(f"\n✅ Word-by-word Translation completed! Output saved: {output_path}")
        else:
            print(f"\n✅ Transliteration completed! Output saved: {output_path}")

        # Verify the output contains no Tamil characters
        print()
        verify_no_tamil_in_file(output_path)

    except Exception as e:
        print(f"❌ Error processing file: {e}")

if __name__ == "__main__":
    main()
